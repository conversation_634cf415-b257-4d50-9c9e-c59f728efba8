<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="联系人姓名" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入联系人姓名" @keyup.enter="getList" />
            </el-form-item>
            <el-form-item label="微信号" prop="wechatNo">
                <el-input v-model="queryParams.wechatNo" placeholder="请输入微信号" @keyup.enter="getList" />
            </el-form-item>
            <el-form-item label="单位名称" prop="unitName">
                <el-input v-model="queryParams.unitName" placeholder="请输入单位名称" @keyup.enter="getList" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="getList">搜索</el-button>
                <el-button icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list">
            <el-table-column label="头像" align="center" width="120">
                <template #default="scope">
                    <el-avatar :size="40" :src="scope.row.avatar" fit="cover">
                        <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png" />
                    </el-avatar>
                </template>
            </el-table-column>
            <el-table-column label="联系人姓名" align="center" prop="name" />
            <el-table-column label="性别" align="center" width="80">
                <template #default="scope">
                    {{ scope.row.gender === 1 ? '男' : '女' }}
                </template>
            </el-table-column>
            <el-table-column label="单位名称" align="center" prop="unitName" />
            <el-table-column label="微信号" align="center" prop="wechatNo" />
            <el-table-column label="类型" align="center">
                <template #default="scope">
                    {{ scope.row.type === 1 ? '微信用户' : '企业微信用户' }}
                </template>
            </el-table-column>
            <el-table-column label="操作" width="280" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 修改联系人对话框 -->
        <el-dialog :title="title" v-model="dialogVisible" width="600px" append-to-body @close="handleClose">
            <el-form ref="dataForm" :model="dataForm" :rules="dataRules" label-width="120px">
                <el-form-item label="联系人姓名" prop="name">
                    <el-input v-model="dataForm.name" placeholder="请输入联系人姓名" />
                </el-form-item>
                <el-form-item label="单位名称" prop="unitName">
                    <el-input v-model="dataForm.unitName" placeholder="请输入单位名称" />
                </el-form-item>
                <el-form-item label="微信号" prop="wechatNo">
                    <el-input v-model="dataForm.wechatNo" placeholder="请输入微信号" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { getList, updatecontacts, delcontacts } from "@/api/manage/contacts";
export default {
    data() {
        return {
            showSearch: true,
            isChange: false,
            list: [],
            loading: false,
            queryParams: {
                unitName: '',
                name: '',
                wechatNo: '',
                pageNum: 1,
                pageSize: 10
            },
            total: 0,
            dialogVisible: false,
            title: '',
            editId: null,
            dataForm: {
                unitName: "",
                name: "",
                wechatNo: ""
            },
            dataRules: {
                unitName: [
                    { required: true, message: '单位名称不能为空', trigger: 'blur' }
                ],
                name: [
                    { required: true, message: '联系人姓名不能为空', trigger: 'blur' }
                ],
                wechatNo: [
                    { required: true, message: '微信号不能为空', trigger: 'blur' }
                ]
            }
        }
    },
    mounted() {
        this.getList()
    },
    methods: {

        submitForm() {
            this.$refs.dataForm.validate(async (valid) => {
                if (valid) {
                    let dataForm = JSON.parse(JSON.stringify(this.dataForm));
                    dataForm.wechatId = this.editId;
                    let res = await updatecontacts(dataForm)
                    if (res.code == 200) {
                        this.$message({
                            message: '修改成功',
                            type: 'success'
                        });
                        this.getList()
                        this.dialogVisible = false
                    }
                } else {
                    return false;
                }
            });
        },

        handleClose() {
            this.$refs['dataForm'].resetFields();
            this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            this.dialogVisible = false;
        },
        async handleEdit(val) {
            this.title = "修改联系人";
            // 只获取需要编辑的字段
            this.dataForm = {
                name: val.name,
                unitName: val.unitName,
                wechatNo: val.wechatNo
            };
            this.editId = val.wechatId; // 设置编辑时的ID
            this.dialogVisible = true;
            this.isChange = true;
        },
        handleDelete(val) {
            this.$confirm('确认删除吗？')
                .then(async (_) => {
                    let res = await delcontacts({ ids: val.wechatId })
                    if (res.code == 200) {
                        this.$message({
                            message: '删除成功',
                            type: 'success'
                        });
                        this.getList()
                    }
                })
        },
        reset() {
            this.queryParams = {
                unitName: '',
                name: '',
                pageNum: 1,
                pageSize: 10
            };
            this.getList()
        },
        async getList() {
            this.loading = true;
            let res = await getList(this.queryParams)
            if (res.code == 200) {
                this.total = res.total;
                this.list = res.rows;
                this.loading = false;
            }
        }
    },
}

</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    background: #FFFFFF;
    border-radius: 14px;
    position: relative;
}
</style>
