import request from '@/utils/request'

// 查询联系人列表
export function getList(query) {
  return request({
    url: '/wechat/user/getUserList',
    method: 'get',
    params: query
  })
}

// 修改联系人
export function updatecontacts(data) {
  return request({
    url: '/wechat/user/editUser',
    method: 'post',
    data: data
  })
}

// 删除联系人
export function delcontacts(data) {
  return request({
    url: '/wechat/user/deleteUser',
    method: 'post',
    data: data
  })
}
